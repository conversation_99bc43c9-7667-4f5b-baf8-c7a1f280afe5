{"name": "flowist", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2"}, "devDependencies": {"@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "typescript": "~5.6.2", "vite": "^6.0.3", "@tauri-apps/cli": "^2"}}