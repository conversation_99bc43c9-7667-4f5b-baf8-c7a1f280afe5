{"name": "flowist", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@tiptap/extension-placeholder": "^3.0.7", "@tiptap/extension-typography": "^3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "@types/node": "^24.0.15", "clsx": "^2.1.1", "framer-motion": "^12.23.6", "lucide-react": "^0.525.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.7.0", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tauri-apps/cli": "^2", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "~5.6.2", "vite": "^6.0.3"}}