import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { LoginForm } from './LoginForm';
import { SetupForm } from './SetupForm';
import { useAuthStore } from '../../stores/authStore';

const AuthScreen: React.FC = () => {
  const [isSetupMode, setIsSetupMode] = useState(false);
  const [isCheckingSetup, setIsCheckingSetup] = useState(true);
  
  const { checkSetupRequired } = useAuthStore();

  useEffect(() => {
    const checkSetup = async () => {
      try {
        console.log('Testing Tauri connection...');

        // First test if <PERSON><PERSON> commands work at all
        try {
          const testResult = await invoke('test_connection');
          console.log('Tauri test result:', testResult);
        } catch (testError) {
          console.error('Tauri connection test failed:', testError);
          throw new Error('Tauri connection failed');
        }

        console.log('Checking setup status...');

        // Add a timeout to prevent infinite loading
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Setup check timeout')), 5000)
        );

        const setupRequired = await Promise.race([
          checkSetupRequired(),
          timeoutPromise
        ]);

        console.log('Setup required:', setupRequired);
        setIsSetupMode(setupRequired as boolean);
      } catch (error) {
        console.error('Error checking setup status:', error);
        // Default to setup mode if check fails (safer for first run)
        setIsSetupMode(true);
      } finally {
        setIsCheckingSetup(false);
      }
    };

    checkSetup();
  }, [checkSetupRequired]);

  if (isCheckingSetup) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading Flowist...</p>
        </div>
      </div>
    );
  }

  if (isSetupMode) {
    return (
      <SetupForm 
        onSwitchToLogin={() => setIsSetupMode(false)} 
      />
    );
  }

  return (
    <LoginForm 
      onSwitchToSetup={() => setIsSetupMode(true)} 
    />
  );
};

export { AuthScreen };
